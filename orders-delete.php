
<?php
include 'config.php';
include 'auth.php';

// Get the order ID from the query string
$id = $_GET['id'];

// Check if the order is referenced in the orders_reservations table
$orders_reservationCheckQuery = "SELECT COUNT(*) as count FROM orders_reservations WHERE order_id = $id";
$orders_reservationCheckResult = mysqli_query($conn, $orders_reservationCheckQuery);
$orders_reservationCheckRow = mysqli_fetch_assoc($orders_reservationCheckResult);

if ($orders_reservationCheckRow['count'] > 0) {
    // Set error message if the reservation is referenced
    $_SESSION['alert_text'] = 'ไม่สามารถลบข้อมูลได้ เนื่องจากมีการอ้างอิงในใบแจ้งหนี้';
    $_SESSION['alert_icon'] = 'error';
} else {
    // Restore product quantities before deleting order
    $restore_query = "
        UPDATE products_orders po 
        INNER JOIN order_details od ON po.product_code = od.product_code 
        SET po.quantity = po.quantity - od.quantity, po.order_id = 0 
        WHERE od.order_id = $id
    ";
    mysqli_query($conn, $restore_query);
    
    // Delete order details
    $ordersDetailsQuery = "DELETE FROM order_details WHERE order_id = $id";
    mysqli_query($conn, $ordersDetailsQuery);

    // Delete the order
    $orderQuery = "DELETE FROM orders WHERE id = $id";
    mysqli_query($conn, $orderQuery);
    
    // Update requisitions to remove order reference
    $update_requisition_query = "UPDATE requisitions SET order_id = NULL WHERE order_id = $id";
    mysqli_query($conn, $update_requisition_query);

    // Set success message
    $_SESSION['alert_text'] = 'ลบข้อมูลเรียบร้อยแล้ว';
    $_SESSION['alert_icon'] = 'success';
}

// Redirect to the orders list
header('Location: ' . $base_url . '/orders-list.php');
exit;
?>

